<?php
#region DOCS

/** @var ControlBase|null $control_base */
/** @var GastoOperativo[] $gastos_operativos */
/** @var float $total_gastos */
/** @var string $fecha_actual */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Control Operativo Diario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- CSS for currency formatting -->
	<style>
        input.currency-input {
            text-align: right;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Control Operativo Diario</h4>
				<p class="mb-0 text-muted">Gestión de control base y gastos operativos para <?php echo date('Y-m-d', strtotime($fecha_actual)); ?></p>
			</div>
		</div>
		<?php #endregion PAGE HEADER ?>

		<?php #region CONTROL BASE SECTION ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Base del Día
				</h4>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-md-4">
						<div class="card bg-primary text-white">
							<div class="card-body">
								<h5 class="card-title">Valor Inicial</h5>
								<h3 class="mb-0" id="valor-inicial-display">
									<?php echo $control_base ? '$' . number_format($control_base->getValor_inicial(), 0, ',', '.') : '$0'; ?>
								</h3>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class="card bg-success text-white">
							<div class="card-body">
								<h5 class="card-title">Valor Actual</h5>
								<h3 class="mb-0" id="valor-actual-display">
									<?php echo $control_base ? '$' . number_format($control_base->getValor_actual(), 0, ',', '.') : '$0'; ?>
								</h3>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class="d-flex align-items-center h-100">
							<?php if ($control_base): ?>
								<button type="button" class="btn btn-warning btn-lg w-100" data-bs-toggle="modal" data-bs-target="#editControlBaseModal">
									<i class="fa fa-edit fa-fw me-1"></i> Abonar base
								</button>
							<?php else: ?>
<button type="button" class="btn btn-primary btn-lg w-100" data-bs-toggle="modal" data-bs-target="#createControlBaseModal">
	<i class="fa fa-plus-circle fa-fw me-1"></i> Definir base
</button>
							<?php endif; ?>
						</div>
					</div>
				</div>
			</div>
		</div>
		<?php #endregion CONTROL BASE SECTION ?>

		<?php #region GASTOS OPERATIVOS SECTION ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading">
                <h4 class="panel-title fs-18px">Gastos Operativos del Día</h4>
                <div class="mb-auto d-flex">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createGastoModal">
                        <i class="fa fa-plus-circle fa-fw me-1"></i> Nuevo Gasto
                    </button>
                </div>
			</div>
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE GASTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th class="text-center">Fecha</th>
						<th>Descripción</th>
						<th class="text-end">Valor</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="gastos-table-body">
					<?php foreach ($gastos_operativos as $gasto): ?>
						<tr data-gasto-id="<?php echo $gasto->getId(); ?>">
							<td class="text-center">
								<button type="button" class="btn btn-xs btn-danger btn-eliminar-gasto"
								        title="Eliminar Gasto"
								        data-gasto-id="<?php echo $gasto->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($gasto->getDescripcion() ?? ''); ?>"
								        data-valor="<?php echo $gasto->getValor(); ?>">
									<i class="fa fa-trash-alt"></i>
								</button>
							</td>
							<td class="text-center"><?php echo date('Y-m-d', strtotime($gasto->getFecha())); ?></td>
							<td class="gasto-descripcion-cell"><?php echo htmlspecialchars($gasto->getDescripcion()); ?></td>
							<td class="text-end gasto-valor-cell"><?php echo $gasto->getValorFormateado(); ?></td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($gastos_operativos)): ?>
						<tr id="no-gastos-row">
							<td colspan="4" class="text-center">No hay gastos operativos para mostrar.</td>
						</tr>
					<?php endif; ?>
					</tbody>
					<tfoot>
					<tr class="table-info">
						<td colspan="3" class="text-end fw-bold">Total Gastos:</td>
						<td class="text-end fw-bold" id="total-gastos-display">
							<?php echo '$' . number_format($total_gastos, 0, ',', '.'); ?>
						</td>
					</tr>
					</tfoot>
				</table>
				<?php #endregion TABLE GASTOS ?>
			</div>
		</div>
		<?php #endregion GASTOS OPERATIVOS SECTION ?>

		<?php #region Create Control Base Modal ?>
		<div class="modal fade" id="createControlBaseModal" tabindex="-1" aria-labelledby="createControlBaseModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-control-base-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createControlBaseModalLabel">Definir base</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear_control_base">

							<div class="mb-3">
								<label for="create-valor-inicial" class="form-label">Valor Inicial:</label>
								<input type="text" class="form-control currency-input" id="create-valor-inicial" name="valor_inicial"
								       data-type="currency" placeholder="$0" required>
								<div class="form-text">Ingrese el valor inicial para el control base del día</div>
							</div>

							<div class="alert alert-danger" id="create-control-base-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Definir base</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Control Base Modal ?>

		<?php #region Edit Control Base Modal ?>
		<div class="modal fade" id="editControlBaseModal" tabindex="-1" aria-labelledby="editControlBaseModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-control-base-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editControlBaseModalLabel">Abonar a base</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="editar_control_base">

							<div class="mb-3">
								<label for="edit-valor-adicional" class="form-label">Valor adicional:</label>
								<input type="text" class="form-control currency-input" id="edit-valor-adicional" name="valor_adicional"
								       data-type="currency" required placeholder="$0">
								<div class="form-text">Ingrese el valor adicional para agregar al control base</div>
							</div>

							<div class="alert alert-danger" id="edit-control-base-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-warning">Agregar Valor</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Control Base Modal ?>

		<?php #region Create Gasto Modal ?>
		<div class="modal fade" id="createGastoModal" tabindex="-1" aria-labelledby="createGastoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-gasto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createGastoModalLabel">Crear Gasto Operativo</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear_gasto">

							<div class="mb-3">
								<label for="create-gasto-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="create-gasto-descripcion" name="descripcion"
								       required placeholder="Ej: Compra de materiales">
							</div>

							<div class="mb-3">
								<label for="create-gasto-valor" class="form-label">Valor:</label>
								<input type="text" class="form-control currency-input" id="create-gasto-valor" name="valor"
								       data-type="currency" placeholder="$0" required>
							</div>

							<div class="alert alert-danger" id="create-gasto-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-success">Crear Gasto</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Gasto Modal ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Modal Elements
        const createControlBaseModal = new bootstrap.Modal(document.getElementById('createControlBaseModal'));
        const editControlBaseModal   = new bootstrap.Modal(document.getElementById('editControlBaseModal'));
        const createGastoModal       = new bootstrap.Modal(document.getElementById('createGastoModal'));

        // Form Elements
        const createControlBaseForm = document.getElementById('create-control-base-form');
        const editControlBaseForm   = document.getElementById('edit-control-base-form');
        const createGastoForm       = document.getElementById('create-gasto-form');

        // Error Elements
        const createControlBaseError = document.getElementById('create-control-base-error');
        const editControlBaseError   = document.getElementById('edit-control-base-error');
        const createGastoError       = document.getElementById('create-gasto-error');

        // Display Elements
        const valorInicialDisplay = document.getElementById('valor-inicial-display');
        const valorActualDisplay  = document.getElementById('valor-actual-display');
        const totalGastosDisplay  = document.getElementById('total-gastos-display');
        const gastosTableBody     = document.getElementById('gastos-table-body');

        // Initialize currency formatting for dynamically added elements
        $('#createControlBaseModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur : function() { formatCurrency($(this), "blur"); }
                });
            });
        });

        $('#editControlBaseModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur : function() { formatCurrency($(this), "blur"); }
                });
            });
        });

        $('#createGastoModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur: function() { formatCurrency($(this), "blur"); }
                });
            });
        });

        // Utility function to format currency
        function formatCurrency(value) {
            return '$' + Math.round(value).toLocaleString('es-CO').replace(/,/g, '.');
        }

        // --- Handle Create Control Base Form Submission ---
        if (createControlBaseForm) {
            createControlBaseForm.addEventListener('submit', function (event) {
                event.preventDefault();
                createControlBaseError.style.display = 'none';

                const formData = new FormData(createControlBaseForm);
                const valorInicial = parseFloat(formData.get('valor_inicial').replace(/[$,]/g, '').replace(/\./g, '').replace(/,/g, '.'));

                // Basic client-side validation
                if (isNaN(valorInicial) || valorInicial < 0) {
                    createControlBaseError.textContent = 'El valor inicial debe ser un número válido mayor o igual a cero.';
                    createControlBaseError.style.display = 'block';
                    return;
                }

                // Update formData with the parsed numeric value
                formData.set('valor_inicial', valorInicial);

                // Disable submit button during request
                const submitButton = createControlBaseForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('gasto-operativo', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                        }).catch(() => {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        createControlBaseModal.hide();
                        createControlBaseForm.reset();

                        // Update display values
                        valorInicialDisplay.textContent = data.control_base.valor_inicial_formateado;
                        valorActualDisplay.textContent = data.control_base.valor_actual_formateado;

                        // Update button to edit mode
                        const buttonContainer = document.querySelector('.btn-primary.btn-lg');
                        if (buttonContainer) {
                            buttonContainer.className = 'btn btn-warning btn-lg w-100';
                            buttonContainer.setAttribute('data-bs-target', '#editControlBaseModal');
                            buttonContainer.innerHTML = '<i class="fa fa-edit fa-fw me-1"></i> Abonar base';
                        }

                        showSweetAlertSuccess('Éxito', data.message);
                    } else {
                        createControlBaseError.textContent = data.message || 'Ocurrió un error al crear el Control Base.';
                        createControlBaseError.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error creating Control Base:', error);
                    createControlBaseError.textContent = 'Error de red o del servidor: ' + error.message;
                    createControlBaseError.style.display = 'block';
                })
                .finally(() => {
                    submitButton.disabled = false;
                });
            });
        }

        // --- Handle Edit Control Base Form Submission ---
        if (editControlBaseForm) {
            editControlBaseForm.addEventListener('submit', function (event) {
                event.preventDefault();
                editControlBaseError.style.display = 'none';

                const formData       = new FormData(editControlBaseForm);
                const valorAdicional = parseFloat(formData.get('valor_adicional').replace(/[$,]/g, '').replace(/\./g, '').replace(/,/g, '.'));

                // Basic client-side validation
                if (isNaN(valorAdicional) || valorAdicional <= 0) {
                    editControlBaseError.textContent   = 'El valor adicional debe ser un número válido mayor que cero.';
                    editControlBaseError.style.display = 'block';
                    return;
                }

                // Update formData with the parsed numeric value
                formData.set('valor_adicional', valorAdicional);

                // Disable submit button during request
                const submitButton = editControlBaseForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('gasto-operativo', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                        }).catch(() => {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        editControlBaseModal.hide();
                        editControlBaseForm.reset();

                        // Update display values
                        valorInicialDisplay.textContent = data.control_base.valor_inicial_formateado;
                        valorActualDisplay.textContent  = data.control_base.valor_actual_formateado;

                        showSweetAlertSuccess('Éxito', data.message);
                    } else {
                        editControlBaseError.textContent   = data.message || 'Ocurrió un error al actualizar el Control Base.';
                        editControlBaseError.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error updating Control Base:', error);
                    editControlBaseError.textContent   = 'Error de red o del servidor: ' + error.message;
                    editControlBaseError.style.display = 'block';
                })
                .finally(() => {
                    submitButton.disabled = false;
                });
            });
        }

        // --- Function to check if ControlBase exists for today's date ---
        function checkControlBaseExistsToday() {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('action', 'verificar_control_base');
                // No need to send fecha - server will use current date

                fetch('gasto-operativo', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                        }).catch(() => {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    resolve(data.exists);
                })
                .catch(error => {
                    console.error('Error checking ControlBase:', error);
                    reject(error);
                });
            });
        }

        // --- Handle Create Gasto Form Submission ---
        if (createGastoForm) {
            createGastoForm.addEventListener('submit', function (event) {
                event.preventDefault();
                createGastoError.style.display = 'none';

                const formData    = new FormData(createGastoForm);
                const descripcion = formData.get('descripcion').trim();
                const valor       = parseFloat(formData.get('valor').replace(/[$,]/g, '').replace(/\./g, '').replace(/,/g, '.'));

                // Basic client-side validation
                if (!descripcion) {
                    createGastoError.textContent   = 'La descripción no puede estar vacía.';
                    createGastoError.style.display = 'block';
                    return;
                }

                if (isNaN(valor) || valor <= 0) {
                    createGastoError.textContent   = 'El valor debe ser un número válido mayor que cero.';
                    createGastoError.style.display = 'block';
                    return;
                }

                // Update formData with the parsed numeric value
                formData.set('valor', valor);

                // Disable submit button during validation
                const submitButton = createGastoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                submitButton.textContent = 'Verificando...';

                // Check if ControlBase exists for today's date
                checkControlBaseExistsToday()
                .then(exists => {
                    if (!exists) {
                        // Show SweetAlert error with guidance
                        swal({
                            title: "Control Base Requerido",
                            text: "No existe un Control Base para el día de hoy. Debe crear primero un Control Base para poder registrar gastos operativos.",
                            icon: "warning",
                            buttons: {
                                cancel: {
                                    text      : "Cancelar",
                                    value     : null,
                                    visible   : true,
                                    className : "btn-secondary",
                                    closeModal: true,
                                },
                                confirm: {
                                    text      : "Crear Control Base",
                                    value     : true,
                                    visible   : true,
                                    className : "btn-primary",
                                    closeModal: true
                                }
                            }
                        })
                        .then((willCreate) => {
                            if (willCreate) {
                                // Close the gasto modal and open the control base modal
                                createGastoModal.hide();
                                createControlBaseModal.show();
                            }
                        });
                        return;
                    }

                    // ControlBase exists, proceed with gasto creation
                    submitGastoForm(formData, submitButton);
                })
                .catch(error => {
                    createGastoError.textContent = 'Error al verificar Control Base: ' + error.message;
                    createGastoError.style.display = 'block';
                })
                .finally(() => {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Crear Gasto';
                });
            });
        }

        // --- Function to submit gasto form after validation ---
        function submitGastoForm(formData, submitButton) {
            fetch('gasto-operativo', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                    }).catch(() => {
                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    createGastoModal.hide();
                    createGastoForm.reset();

                    // Remove "no data" row if it exists
                    const noDataRow = gastosTableBody.querySelector('#no-gastos-row');
                    if (noDataRow) {
                        noDataRow.remove();
                    }

                    // Create new row HTML
                    const newRow = document.createElement('tr');
                    newRow.setAttribute('data-gasto-id', data.gasto.id);

                    newRow.innerHTML = `
                        <td class="text-center">
                            <button type="button" class="btn btn-xs btn-danger btn-eliminar-gasto"
                                    title="Eliminar Gasto"
                                    data-gasto-id="${data.gasto.id}"
                                    data-descripcion="${data.gasto.descripcion}"
                                    data-valor="${data.gasto.valor}">
                                <i class="fa fa-trash-alt"></i>
                            </button>
                        </td>
                        <td class="text-center">${new Date(data.gasto.fecha).toISOString().split('T')[0]}</td>
                        <td class="gasto-descripcion-cell">${data.gasto.descripcion}</td>
                        <td class="text-end gasto-valor-cell">${data.gasto.valor_formateado}</td>
                    `;

                    // Add the new row to the table
                    gastosTableBody.appendChild(newRow);

                    // Update total gastos
                    updateTotalGastos();

                    // Update valor_actual display if control_base data is provided
                    if (data.control_base && data.control_base.valor_actual_formateado) {
                        valorActualDisplay.textContent = data.control_base.valor_actual_formateado;
                    }

                    showSweetAlertSuccess('Éxito', data.message);
                } else {
                    createGastoError.textContent = data.message || 'Ocurrió un error al crear el Gasto Operativo.';
                    createGastoError.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error creating Gasto:', error);
                createGastoError.textContent = 'Error de red o del servidor: ' + error.message;
                createGastoError.style.display = 'block';
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.textContent = 'Crear Gasto';
            });
        }

        // --- Handle Delete Gasto Click (Event Delegation) ---
        if (gastosTableBody) {
            gastosTableBody.addEventListener('click', function (event) {
                const deleteButton = event.target.closest('.btn-eliminar-gasto');

                if (deleteButton) {
                    event.preventDefault();
                    const gastoId = deleteButton.dataset.gastoId;
                    const descripcion = deleteButton.dataset.descripcion;
                    const valor = parseFloat(deleteButton.dataset.valor);

                    // Confirm before deleting
                    swal({
                        title: "¿Estás seguro?",
                        text: `¿Deseas eliminar el gasto "${descripcion}" por ${formatCurrency(valor)}?`,
                        icon: "warning",
                        buttons: {
                            cancel: {
                                text      : "Cancelar",
                                value     : null,
                                visible   : true,
                                className : "btn-secondary",
                                closeModal: true,
                            },
                            confirm: {
                                text      : "Sí, eliminar",
                                value     : true,
                                visible   : true,
                                className : "btn-danger",
                                closeModal: true
                            }
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete) {
                            // User confirmed, delete the gasto
                            const formData = new FormData();
                            formData.append('action', 'eliminar_gasto');
                            formData.append('gasto_id', gastoId);

                            fetch('gasto-operativo', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => {
                                if (!response.ok) {
                                    return response.json().then(errData => {
                                        throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                                    }).catch(() => {
                                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                                    });
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data.success) {
                                    // Remove the row from the table
                                    const row = gastosTableBody.querySelector(`tr[data-gasto-id="${gastoId}"]`);
                                    if (row) {
                                        row.remove();
                                    }

                                    // Check if table is empty and add "no data" row
                                    const remainingRows = gastosTableBody.querySelectorAll('tr[data-gasto-id]');
                                    if (remainingRows.length === 0) {
                                        const noDataRow = document.createElement('tr');
                                        noDataRow.id = 'no-gastos-row';
                                        noDataRow.innerHTML = '<td colspan="4" class="text-center">No hay gastos operativos para mostrar.</td>';
                                        gastosTableBody.appendChild(noDataRow);
                                    }

                                    // Update total gastos
                                    updateTotalGastos();

                                    // Update valor_actual display if control_base data is provided
                                    if (data.control_base && data.control_base.valor_actual_formateado) {
                                        valorActualDisplay.textContent = data.control_base.valor_actual_formateado;
                                    }

                                    showSweetAlertSuccess('Éxito', data.message);
                                } else {
                                    showSweetAlertError('Error', data.message || 'Ocurrió un error al eliminar el gasto.');
                                }
                            })
                            .catch(error => {
                                console.error('Error deleting Gasto:', error);
                                showSweetAlertError('Error', 'Error de red o del servidor: ' + error.message);
                            });
                        }
                    });
                }
            });
        }

        // --- Utility function to update total gastos ---
        function updateTotalGastos() {
            let total = 0;
            const valorCells = gastosTableBody.querySelectorAll('.gasto-valor-cell');

            valorCells.forEach(cell => {
                // Extract numeric value from formatted currency
                const valorText = cell.textContent.replace(/[$.,]/g, '');
                const valor = parseFloat(valorText) || 0;
                total += valor;
            });

            totalGastosDisplay.textContent = formatCurrency(total);
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
