<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class GastoOperativo
{
	// --- Atributos ---
	private ?int    $id              = null;
	private ?string $descripcion     = null;
	private ?string $fecha           = null;
	private ?float  $valor           = null;
	private ?int    $id_cierre       = null;
	private ?int    $id_centro_costo = null; 
	private ?int    $estado          = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto GastoOperativo.
	 *
	 * @param string|null $descripcion     Descripción del gasto operativo (obligatorio)
	 * @param string|null $fecha           Fecha del gasto (obligatorio, formato YYYY-MM-DD)
	 * @param float|null  $valor           Valor del gasto (obligatorio, debe ser > 0)
	 * @param int|null    $id_centro_costo ID del centro de costo (opcional)
	 */
	public function __construct(?string $descripcion = null, ?string $fecha = null, ?float $valor = null, ?int $id_centro_costo = null)
	{
		// Establecer la zona horaria para Colombia
		date_default_timezone_set('America/Bogota');

		$this->id              = 0;
		$this->descripcion     = $descripcion;
		$this->fecha           = $fecha ?? date('Y-m-d'); // Fecha actual por defecto
		$this->valor           = $valor;
		$this->id_cierre       = null; // Inicialmente null
		$this->id_centro_costo = $id_centro_costo; // Asignar el nuevo atributo
		$this->estado          = 1;    // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto GastoOperativo desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del gasto operativo.
	 *
	 * @return self Instancia de GastoOperativo.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                  = new self();
			$objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion     = $resultado['descripcion'] ?? null;
			$objeto->fecha           = $resultado['fecha'] ?? null;
			$objeto->valor           = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
			$objeto->id_cierre       = isset($resultado['id_cierre']) ? (int)$resultado['id_cierre'] : null;
			$objeto->id_centro_costo = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null; // Construir nuevo atributo
			$objeto->estado          = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir GastoOperativo: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un gasto operativo por su ID.
	 *
	 * @param int $id       ID del gasto operativo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto GastoOperativo o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener gasto operativo por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoOperativo (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todos los gastos operativos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de gastos operativos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.estado = 1
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de GastoOperativo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos por fecha.
	 *
	 * @param string $fecha    Fecha a filtrar (formato YYYY-MM-DD).
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorFecha(string $fecha, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos por fecha (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.fecha = :fecha AND go.estado = 1
            ORDER BY
            	go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha", $fecha, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoOperativo por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos por fecha y centro de costo.
	 *
	 * @param string $fecha           Fecha a filtrar (formato YYYY-MM-DD).
	 * @param int    $id_centro_costo ID del centro de costo.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorFechaYCentroCosto(string $fecha, int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos por fecha y centro de costo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.fecha = :fecha
            	AND go.estado = 1
            	AND go.id_centro_costo = :id_centro_costo
            ORDER BY
            	go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha", $fecha, PDO::PARAM_STR);
			$statement->bindValue(":id_centro_costo", $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoOperativo por fecha ($fecha) y centro de costo ($id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos por rango de fechas.
	 *
	 * @param string $fecha_inicio Fecha de inicio (formato YYYY-MM-DD).
	 * @param string $fecha_fin    Fecha de fin (formato YYYY-MM-DD).
	 * @param PDO    $conexion     Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorRangoFechas(string $fecha_inicio, string $fecha_fin, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos por rango de fechas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.fecha BETWEEN :fecha_inicio AND :fecha_fin
            	AND go.estado = 1
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha_inicio", $fecha_inicio, PDO::PARAM_STR);
			$statement->bindValue(":fecha_fin", $fecha_fin, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoOperativo por rango de fechas ($fecha_inicio - $fecha_fin): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos por ID de cierre.
	 *
	 * @param int $id_cierre ID del cierre.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorCierre(int $id_cierre, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos por ID de cierre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.id_cierre = :id_cierre AND go.estado = 1
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_cierre", $id_cierre, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoOperativo por cierre (ID: $id_cierre): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo gasto operativo en la base de datos a partir de un objeto GastoOperativo.
	 * El objeto GastoOperativo debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo gasto operativo creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || empty($this->getFecha()) || $this->getValor() === null) {
			throw new Exception("Descripción, fecha y valor son requeridos en el objeto GastoOperativo para crearlo.");
		}

		// Validar descripción no vacía
		if (!$this->validarDescripcion($this->getDescripcion())) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		// Validar fecha válida
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		// Validar valor positivo
		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		// Validar que el cierre existe (si se especifica)
		if ($this->getId_cierre() !== null && !$this->validarCierreExiste($this->getId_cierre(), $conexion)) {
			throw new Exception("El cierre especificado no existe.");
		}

		// Validar que el centro de costo existe (si se especifica)
		if ($this->getId_centro_costo() !== null && !$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
			throw new Exception("El centro de costo especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			return $this->_insert($conexion);

		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear gasto operativo: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un gasto operativo existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un GastoOperativo.");
		}

		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || empty($this->getFecha()) || $this->getValor() === null) {
			throw new Exception("Descripción, fecha y valor son requeridos en el objeto GastoOperativo para modificarlo.");
		}

		// Validar descripción no vacía
		if (!$this->validarDescripcion($this->getDescripcion())) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		// Validar fecha válida
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		// Validar valor positivo
		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		// Validar que el cierre existe (si se especifica)
		if ($this->getId_cierre() !== null && !$this->validarCierreExiste($this->getId_cierre(), $conexion)) {
			throw new Exception("El cierre especificado no existe.");
		}

		// Validar que el centro de costo existe (si se especifica)
		if ($this->getId_centro_costo() !== null && !$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
			throw new Exception("El centro de costo especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			return $this->_update($conexion);

		} catch (Exception $e) {
			throw new Exception("Error al modificar gasto operativo: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo gasto operativo en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo gasto operativo creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO gastos_operativos (
            	 descripcion
            	,fecha
            	,valor
            	,id_cierre
            	,id_centro_costo
            	,estado
            ) VALUES (
            	 :descripcion
            	,:fecha
            	,:valor
            	,:id_cierre
            	,:id_centro_costo
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':id_cierre', $this->getId_cierre(), PDO::PARAM_INT);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT); // Bind nuevo atributo
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del gasto operativo recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al insertar gasto operativo: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al insertar gasto operativo: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un gasto operativo existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el gasto operativo
			$query = <<<SQL
            UPDATE gastos_operativos SET
                descripcion = :descripcion,
                fecha = :fecha,
                valor = :valor,
                id_cierre = :id_cierre,
                id_centro_costo = :id_centro_costo,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':id_cierre', $this->getId_cierre(), PDO::PARAM_INT);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT); // Bind nuevo atributo
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al actualizar gasto operativo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un gasto operativo por su ID (eliminación lógica - cambia estado a 0).
	 *
	 * @param int $id       ID del gasto operativo a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar lógicamente el gasto operativo (cambiar estado a 0)
			$query = <<<SQL
            UPDATE gastos_operativos SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar gasto operativo (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de Validación ---

	/**
	 * Valida que la descripción no esté vacía.
	 *
	 * @param string|null $descripcion Descripción a validar.
	 *
	 * @return bool True si la descripción es válida, False en caso contrario.
	 */
	private function validarDescripcion(?string $descripcion): bool
	{
		return !empty(trim($descripcion ?? ''));
	}

	/**
	 * Valida que un valor sea positivo (mayor que cero).
	 *
	 * @param float|null $valor Valor a validar.
	 *
	 * @return bool True si el valor es válido, False en caso contrario.
	 */
	private function validarValorPositivo(?float $valor): bool
	{
		return $valor !== null && is_numeric($valor) && $valor > 0;
	}

	/**
	 * Valida que una fecha tenga el formato correcto (YYYY-MM-DD).
	 *
	 * @param string|null $fecha Fecha a validar.
	 *
	 * @return bool True si la fecha es válida, False en caso contrario.
	 */
	private function validarFecha(?string $fecha): bool
	{
		if (empty($fecha)) {
			return false;
		}

		// Validar formato YYYY-MM-DD usando DateTime
		$dateTime = \DateTime::createFromFormat('Y-m-d', $fecha);
		return $dateTime && $dateTime->format('Y-m-d') === $fecha;
	}

	/**
	 * Valida que un cierre exista en la base de datos.
	 *
	 * @param int|null $id_cierre ID del cierre a validar.
	 * @param PDO      $conexion  Conexión PDO.
	 *
	 * @return bool True si el cierre existe, False en caso contrario.
	 */
	private function validarCierreExiste(?int $id_cierre, PDO $conexion): bool
	{
		if ($id_cierre === null) {
			return false;
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM cierres
            WHERE id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_cierre, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;

		} catch (PDOException $e) {
			// En caso de error, asumir que no existe
			return false;
		}
	}

	/**
	 * Valida que un centro de costo exista en la base de datos.
	 *
	 * @param int|null $id_centro_costo ID del centro de costo a validar.
	 * @param PDO      $conexion        Conexión PDO.
	 *
	 * @return bool True si el centro de costo existe, False en caso contrario.
	 */
	private function validarCentroCostoExiste(?int $id_centro_costo, PDO $conexion): bool
	{
		if ($id_centro_costo === null) {
			return true; // Es válido si es null (opcional)
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM centros_costos
            WHERE id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;
		} catch (PDOException $e) {
			return false; // En caso de error, asumir que no existe
		}
	}

	// --- Getters y Setters ---

	/**
	 * Obtiene el ID del gasto operativo.
	 *
	 * @return int|null ID del gasto operativo.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Establece el ID del gasto operativo.
	 *
	 * @param int|null $id ID del gasto operativo.
	 *
	 * @return self
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Obtiene la descripción del gasto operativo.
	 *
	 * @return string|null Descripción del gasto operativo.
	 */
	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	/**
	 * Establece la descripción del gasto operativo.
	 *
	 * @param string|null $descripcion Descripción del gasto operativo.
	 *
	 * @return self
	 * @throws Exception Si la descripción está vacía.
	 */
	public function setDescripcion(?string $descripcion): self
	{
		if ($descripcion !== null && !$this->validarDescripcion($descripcion)) {
			throw new Exception("La descripción no puede estar vacía.");
		}
		$this->descripcion = $descripcion;
		return $this;
	}

	/**
	 * Obtiene la fecha del gasto operativo.
	 *
	 * @return string|null Fecha del gasto operativo.
	 */
	public function getFecha(): ?string
	{
		return $this->fecha;
	}

	/**
	 * Establece la fecha del gasto operativo.
	 *
	 * @param string|null $fecha Fecha del gasto operativo (formato YYYY-MM-DD).
	 *
	 * @return self
	 * @throws Exception Si la fecha no tiene un formato válido.
	 */
	public function setFecha(?string $fecha): self
	{
		if ($fecha !== null && !$this->validarFecha($fecha)) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}
		$this->fecha = $fecha;
		return $this;
	}

	/**
	 * Obtiene el valor del gasto operativo.
	 *
	 * @return float|null Valor del gasto operativo.
	 */
	public function getValor(): ?float
	{
		return $this->valor;
	}

	/**
	 * Establece el valor del gasto operativo.
	 *
	 * @param float|null $valor Valor del gasto operativo.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValor(?float $valor): self
	{
		if ($valor !== null && !$this->validarValorPositivo($valor)) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}
		$this->valor = $valor;
		return $this;
	}

	/**
	 * Obtiene el ID del cierre.
	 *
	 * @return int|null ID del cierre.
	 */
	public function getId_cierre(): ?int
	{
		return $this->id_cierre;
	}

	/**
	 * Establece el ID del cierre.
	 *
	 * @param int|null $id_cierre ID del cierre.
	 *
	 * @return self
	 * @throws Exception Si el ID no es válido.
	 */
	public function setId_cierre(?int $id_cierre): self
	{
		if ($id_cierre !== null && $id_cierre <= 0) {
			throw new Exception("El ID del cierre debe ser un número entero positivo.");
		}
		$this->id_cierre = $id_cierre;
		return $this;
	}

	/**
	 * Obtiene el ID del centro de costo.
	 *
	 * @return int|null ID del centro de costo.
	 */
	public function getId_centro_costo(): ?int
	{
		return $this->id_centro_costo;
	}

	/**
	 * Establece el ID del centro de costo.
	 *
	 * @param int|null $id_centro_costo ID del centro de costo.
	 *
	 * @return self
	 */
	public function setId_centro_costo(?int $id_centro_costo): self
	{
		if ($id_centro_costo !== null && $id_centro_costo <= 0) {
			throw new Exception("El ID del centro de costo debe ser un número entero positivo.");
		}
		$this->id_centro_costo = $id_centro_costo;
		return $this;
	}

	/**
	 * Obtiene el estado del gasto operativo.
	 *
	 * @return int|null Estado del gasto operativo.
	 */
	public function getEstado(): ?int
	{
		return $this->estado;
	}

	/**
	 * Establece el estado del gasto operativo.
	 *
	 * @param int|null $estado Estado del gasto operativo.
	 *
	 * @return self
	 */
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el gasto operativo está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}

	/**
	 * Verifica si el gasto operativo está asociado a un cierre.
	 * @return bool
	 */
	public function tieneCierre(): bool
	{
		return $this->id_cierre !== null;
	}

	/**
	 * Obtiene el valor formateado en pesos colombianos.
	 * @return string
	 */
	public function getValorFormateado(): string
	{
		if ($this->valor === null) {
			return '$0';
		}
		return '$' . number_format($this->valor, 0, ',', '.');
	}

	/**
	 * Obtiene la fecha formateada para mostrar.
	 * @return string
	 */
	public function getFechaFormateada(): string
	{
		if (empty($this->fecha)) {
			return '';
		}

		try {
			$dateTime = new \DateTime($this->fecha);
			return $dateTime->format('d/m/Y');
		} catch (Exception $e) {
			return $this->fecha;
		}
	}

	/**
	 * Obtiene una descripción resumida del gasto operativo.
	 * @return string
	 */
	public function getResumen(): string
	{
		$descripcion = $this->descripcion ?? 'Sin descripción';
		$valor = $this->getValorFormateado();
		$fecha = $this->getFechaFormateada();

		$resumen = "$descripcion: $valor";
		if (!empty($fecha)) {
			$resumen .= " ($fecha)";
		}

		return $resumen;
	}

	/**
	 * Obtiene el estado como texto.
	 * @return string
	 */
	public function getEstadoTexto(): string
	{
		return match($this->estado) {
			1 => 'Activo',
			0 => 'Inactivo',
			default => 'Desconocido'
		};
	}

	/**
	 * Calcula el total de gastos operativos para una fecha específica.
	 *
	 * @param string $fecha    Fecha a consultar (formato YYYY-MM-DD).
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return float Total de gastos operativos para la fecha.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularTotalPorFecha(string $fecha, PDO $conexion): float
	{
		try {
			$query = <<<SQL
            SELECT COALESCE(SUM(valor), 0) as total
            FROM gastos_operativos
            WHERE fecha = :fecha AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular total de gastos operativos por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Calcula el total de gastos operativos para una fecha específica y centro de costo.
	 *
	 * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
	 * @param int    $id_centro_costo ID del centro de costo.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return float Total de gastos operativos para la fecha y centro de costo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularTotalPorFechaYCentroCosto(string $fecha, int $id_centro_costo, PDO $conexion): float
	{
		try {
			$query = <<<SQL
            SELECT COALESCE(SUM(valor), 0) as total
            FROM gastos_operativos
            WHERE fecha = :fecha
            AND estado = 1
            AND id_centro_costo = :id_centro_costo
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular total de gastos operativos por fecha ($fecha) y centro de costo ($id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Calcula el total de gastos operativos para un rango de fechas.
	 *
	 * @param string $fecha_inicio Fecha de inicio (formato YYYY-MM-DD).
	 * @param string $fecha_fin    Fecha de fin (formato YYYY-MM-DD).
	 * @param PDO    $conexion     Conexión PDO.
	 *
	 * @return float Total de gastos operativos para el rango.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularTotalPorRango(string $fecha_inicio, string $fecha_fin, PDO $conexion): float
	{
		try {
			$query = <<<SQL
            SELECT COALESCE(SUM(valor), 0) as total
            FROM gastos_operativos
            WHERE fecha BETWEEN :fecha_inicio AND :fecha_fin AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_inicio', $fecha_inicio, PDO::PARAM_STR);
			$statement->bindValue(':fecha_fin', $fecha_fin, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular total de gastos operativos por rango ($fecha_inicio - $fecha_fin): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos listos para cierre de caja.
	 * Retorna todos los gastos operativos activos que no han sido cerrados.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getGastosOperativosParaCierre(PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos listos para cierre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.estado = 1
            	AND go.id_cierre IS NULL
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener gastos operativos para cierre: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene gastos operativos listos para cierre de caja filtrados por centro de costo.
	 * Retorna todos los gastos operativos activos que no han sido cerrados para un centro de costo específico.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getGastosOperativosParaCierreByCentroCosto(int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos listos para cierre por centro de costo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.estado = 1
            	AND go.id_cierre IS NULL
            	AND go.id_centro_costo = :id_centro_costo
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener gastos operativos para cierre por centro de costo ($id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Actualiza el id_cierre para múltiples gastos operativos en una sola operación.
	 *
	 * @param array $ids_gastos Array de IDs de gastos operativos a actualizar.
	 * @param int   $id_cierre  ID del cierre a asignar.
	 * @param PDO   $conexion   Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function actualizarIdCierreBulk(array $ids_gastos, int $id_cierre, PDO $conexion): bool
	{
		if (empty($ids_gastos)) {
			return true; // No hay nada que actualizar
		}

		try {
			// Validar y sanitizar todos los IDs para prevenir inyección SQL
			$ids_validados = [];
			foreach ($ids_gastos as $id) {
				$id_int = filter_var($id, FILTER_VALIDATE_INT, [
					'options' => ['min_range' => 1]
				]);
				if ($id_int === false) {
					throw new Exception("ID de gasto operativo inválido detectado: " . var_export($id, true));
				}
				$ids_validados[] = $id_int;
			}

			// Crear placeholders seguros para los IDs validados
			$placeholders = str_repeat('?,', count($ids_validados) - 1) . '?';

			// Consulta para actualizar el id_cierre en múltiples gastos operativos
			$query = <<<SQL
            UPDATE gastos_operativos SET
                id_cierre = ?
            WHERE
                id IN ($placeholders)
                AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);

			// Bind del id_cierre primero, luego los IDs validados
			$params = [$id_cierre];
			$params = array_merge($params, $ids_validados);

			return $statement->execute($params);

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar id_cierre en gastos operativos: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los gastos operativos asociados a un cierre específico.
	 *
	 * @param int $id_cierre ID del cierre.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos GastoOperativo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getGastosOperativosByCierre(int $id_cierre, PDO $conexion): array
	{
		try {
			// Consulta para obtener gastos operativos por cierre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	go.*
            FROM gastos_operativos go
            WHERE
            	go.id_cierre = :id_cierre
            ORDER BY
            	go.fecha DESC, go.id DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cierre', $id_cierre, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener gastos operativos por cierre: " . $e->getMessage());
		}
	}

	/**
	 * Calcula los egresos totales de gastos operativos para una fecha específica.
	 * Solo incluye gastos finalizados (con cierre asignado).
	 *
	 * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
	 * @param int    $id_centro_costo ID del centro de costo para filtrar.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return float Total de egresos de gastos operativos para la fecha.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularEgresosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): float
	{
		try {
			$query = <<<SQL
			SELECT COALESCE(SUM(valor), 0) as total
			FROM gastos_operativos
			WHERE DATE(fecha) = :fecha
			  AND estado = 1
			  AND id_cierre IS NOT NULL
			  AND id_centro_costo = :id_centro_costo
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular egresos de gastos por fecha ($fecha) y centro de costo ($id_centro_costo): " . $e->getMessage());
		}
	}
}
